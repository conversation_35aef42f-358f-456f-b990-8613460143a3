# 婴儿哭声识别系统

基于CH32V307VCT6微控制器的智能婴儿哭声识别与情绪分析系统。

## 功能特性

### 基础功能
- **音频采集**: 通过ADC采集音频信号，使用DMA高效传输
- **实时处理**: 1024点采样，8kHz采样率
- **信号分析**: 零交叉率(ZCR)、能量分析、峰值检测
- **滑动平均**: 5点滑动窗口平滑处理

### 高级情绪识别
系统能够识别以下6种不同的哭声情绪：

1. **愤怒哭声 (Angry)**: 高能量，中高频占主导
2. **饥饿哭声 (Hungry)**: 节奏性强，ZCR较高  
3. **困倦哭声 (Sleepy)**: 低频占主导，能量较低
4. **生病哭声 (Sick)**: 高频能量低，声音虚弱
5. **无聊哭声 (Bored)**: 中频占主导，间歇性
6. **疼痛哭声 (Pain)**: 突然高能量，高频尖锐

### 技术实现
- **FFT频谱分析**: 使用Kiss FFT库进行1024点快速傅里叶变换
- **频段分析**: 
  - 低频段: 100-400Hz
  - 中频段: 400-1200Hz  
  - 高频段: 1200-3000Hz
- **智能算法**: 基于频谱能量比例和时域特征的多维度分析

## 配置参数

### 检测阈值（已优化）
```c
#define ENERGY_THRESHOLD 5000      // 能量阈值(过滤噪声)
#define ZCR_THRESHOLD_HIGH 85      // 高ZCR阈值(说话声)
#define ZCR_THRESHOLD_LOW 45       // 低ZCR阈值(哭声下限)
#define CRY_ENERGY_MIN 50000       // 哭声最小能量阈值
#define CRY_ENERGY_MAX 2000000     // 哭声最大能量阈值
```

### 识别逻辑
- **哭声**: Energy在50000-2000000之间 且 ZCR在45-85之间
- **说话声**: Energy>5000 且 ZCR>85
- **其他声音**: Energy>5000 但不符合上述条件
- **安静**: Energy<5000

### 情绪识别参数
```c
#define ANGRY_ENERGY_RATIO 1.5f    // 愤怒哭声能量比例
#define HUNGRY_ZCR_RATIO 1.2f      // 饥饿哭声ZCR比例
#define SLEEPY_LOW_FREQ_RATIO 2.0f // 困倦哭声低频比例
#define SICK_HIGH_FREQ_RATIO 0.8f  // 生病哭声高频比例
#define BORED_MID_FREQ_RATIO 1.3f  // 无聊哭声中频比例
```

## 输出信息

### 实时监控（新增调试信息）
```
Peak: 1350, Energy: 1842691, ZCR: 59 [E-OK] [Z-OK]
>>> Crying Detected <<<
>>> Hungry Cry Detected <<<
Freq Analysis - Low:1250 Mid:2100 High:800
Ratios - L:0.30 M:0.51 H:0.19
```

**调试标记说明**:
- `[E-OK]`: 能量值在哭声范围内
- `[Z-OK]`: ZCR值在哭声范围内
- 同时显示两个标记时才会触发哭声分析

### 状态显示
- **Quiet**: 环境安静
- **Speaking Detected**: 检测到说话声
- **Crying Detected**: 检测到哭声
- **[情绪类型] Cry Detected**: 具体情绪识别结果

## 硬件要求

- **MCU**: CH32V307VCT6
- **音频输入**: ADC Channel 1 (PA1)
- **采样率**: 8kHz
- **分辨率**: 12位ADC
- **内存**: 至少8KB RAM用于FFT计算

## 使用方法

1. 连接音频输入到PA1引脚
2. 编译并烧录程序到CH32V307VCT6
3. 通过串口(115200波特率)查看识别结果
4. 根据实际环境调整阈值参数

## 算法优化

### 性能特点
- **低延迟**: 600ms检测周期
- **高精度**: 多维度特征融合分析
- **低功耗**: 优化的FFT算法和滑动平均
- **实时性**: 边采集边分析，无缓存延迟

### 可调参数
所有检测阈值和情绪识别参数都可根据实际使用环境进行调整，以获得最佳识别效果。

## 调试指南

### 问题排查
如果无法识别婴儿哭声，请按以下步骤调试：

1. **观察串口输出**，查看是否显示 `[E-OK]` 和 `[Z-OK]` 标记
2. **能量值调整**：
   - 如果婴儿哭声能量值 < 50000，降低 `CRY_ENERGY_MIN`
   - 如果婴儿哭声能量值 > 2000000，提高 `CRY_ENERGY_MAX`
3. **ZCR值调整**：
   - 如果婴儿哭声ZCR < 45，降低 `ZCR_THRESHOLD_LOW`
   - 如果婴儿哭声ZCR > 85，提高 `ZCR_THRESHOLD_HIGH`

### 典型数值参考
- **安静环境**: Energy: 100-500, ZCR: 30-50
- **婴儿哭声**: Energy: 50000-1500000, ZCR: 45-75
- **成人说话**: Energy: 100000-4000000, ZCR: 70-120

## 扩展功能

系统预留了扩展接口，可以添加：
- LED指示灯
- 蜂鸣器报警
- 无线通信模块
- 数据记录功能

---

**重要**: 根据您的测试数据，当前参数已针对实际婴儿哭声进行优化。如仍无法识别，请根据调试指南调整阈值。
