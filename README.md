# 婴儿哭声识别系统

基于CH32V307VCT6微控制器的智能婴儿哭声识别与情绪分析系统。

## 功能特性

### 基础功能
- **音频采集**: 通过ADC采集音频信号，使用DMA高效传输
- **实时处理**: 1024点采样，8kHz采样率
- **信号分析**: 零交叉率(ZCR)、能量分析、峰值检测
- **滑动平均**: 5点滑动窗口平滑处理

### 高级情绪识别
系统能够识别以下6种不同的哭声情绪：

1. **愤怒哭声 (Angry)**: 高能量，中高频占主导
2. **饥饿哭声 (Hungry)**: 节奏性强，ZCR较高  
3. **困倦哭声 (Sleepy)**: 低频占主导，能量较低
4. **生病哭声 (Sick)**: 高频能量低，声音虚弱
5. **无聊哭声 (Bored)**: 中频占主导，间歇性
6. **疼痛哭声 (Pain)**: 突然高能量，高频尖锐

### 技术实现
- **FFT频谱分析**: 使用Kiss FFT库进行1024点快速傅里叶变换
- **频段分析**: 
  - 低频段: 100-400Hz
  - 中频段: 400-1200Hz  
  - 高频段: 1200-3000Hz
- **智能算法**: 基于频谱能量比例和时域特征的多维度分析

## 配置参数

### 检测阈值
```c
#define ENERGY_THRESHOLD 1000      // 能量阈值
#define ZCR_THRESHOLD 100          // 零交叉率阈值
#define CRY_THRESHOLD 200          // 哭声基础阈值
```

### 情绪识别参数
```c
#define ANGRY_ENERGY_RATIO 1.5f    // 愤怒哭声能量比例
#define HUNGRY_ZCR_RATIO 1.2f      // 饥饿哭声ZCR比例
#define SLEEPY_LOW_FREQ_RATIO 2.0f // 困倦哭声低频比例
#define SICK_HIGH_FREQ_RATIO 0.8f  // 生病哭声高频比例
#define BORED_MID_FREQ_RATIO 1.3f  // 无聊哭声中频比例
```

## 输出信息

### 实时监控
```
Peak: 150, Energy: 1200, ZCR: 85
>>> Crying Detected <<<
>>> Hungry Cry Detected <<<
Freq Analysis - Low:1250 Mid:2100 High:800
Ratios - L:0.30 M:0.51 H:0.19
```

### 状态显示
- **Quiet**: 环境安静
- **Speaking Detected**: 检测到说话声
- **Crying Detected**: 检测到哭声
- **[情绪类型] Cry Detected**: 具体情绪识别结果

## 硬件要求

- **MCU**: CH32V307VCT6
- **音频输入**: ADC Channel 1 (PA1)
- **采样率**: 8kHz
- **分辨率**: 12位ADC
- **内存**: 至少8KB RAM用于FFT计算

## 使用方法

1. 连接音频输入到PA1引脚
2. 编译并烧录程序到CH32V307VCT6
3. 通过串口(115200波特率)查看识别结果
4. 根据实际环境调整阈值参数

## 算法优化

### 性能特点
- **低延迟**: 600ms检测周期
- **高精度**: 多维度特征融合分析
- **低功耗**: 优化的FFT算法和滑动平均
- **实时性**: 边采集边分析，无缓存延迟

### 可调参数
所有检测阈值和情绪识别参数都可根据实际使用环境进行调整，以获得最佳识别效果。

## 扩展功能

系统预留了扩展接口，可以添加：
- LED指示灯
- 蜂鸣器报警
- 无线通信模块
- 数据记录功能

---

**注意**: 首次使用时建议在实际环境中测试并调整相关阈值参数，以获得最佳识别效果。
