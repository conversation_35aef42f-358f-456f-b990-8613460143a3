
#include "debug.h"
#include "kiss_fft.h"
#include "kiss_fft_log.h"

/* Global Variable */
u16 TxBuf[1024];
s16 Calibrattion_Val = 0;




#define SAMPLE_SIZE 1024  // ����������
#define ENERGY_THRESHOLD 500000  // ��ʱ������ֵ��������������ֵ��
#define PEAK_THRESHOLD 1000      // ˲ʱ��ֵ��ֵ��������������ֵ��
#define SPEECH_ZCR_THRESHOLD   80   // ˵����ZCR�ձ��
#define SPEECH_ENERGY_VAR_THRESHOLD  20  // ˵�����������С

#define SMOOTH_SIZE 3
#define SAMPLE_SIZE 1024
#define ZCR_THRESHOLD 100
#define ENERGY_THRESHOLD 1000
#define PEAK_THRESHOLD 100

#define FFT_SIZE 1024

uint32_t energy_buf[SMOOTH_SIZE] = {0};
uint16_t peak_buf[SMOOTH_SIZE] = {0};
uint16_t zcr_buf[SMOOTH_SIZE] = {0};
uint8_t smooth_index = 0;


kiss_fft_cfg fft_cfg;
kiss_fft_cpx fft_in[SAMPLE_SIZE];
kiss_fft_cpx fft_out[SAMPLE_SIZE];

/*********************************************************************
 * @fn      ADC_Function_Init
 *
 * @brief   Initializes ADC collection.
 *
 * @return  none
 */
void ADC_Function_Init(void)
{
    ADC_InitTypeDef ADC_InitStructure={0};
    GPIO_InitTypeDef GPIO_InitStructure={0};

    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE );
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1, ENABLE );
    RCC_ADCCLKConfig(RCC_PCLK2_Div8);

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    ADC_DeInit(ADC1);
    ADC_InitStructure.ADC_Mode = ADC_Mode_Independent;
    ADC_InitStructure.ADC_ScanConvMode = DISABLE;
    ADC_InitStructure.ADC_ContinuousConvMode = ENABLE;
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_None;
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
    ADC_InitStructure.ADC_NbrOfChannel = 1;
    ADC_Init(ADC1, &ADC_InitStructure);

    ADC_DMACmd(ADC1, ENABLE);
    ADC_Cmd(ADC1, ENABLE);

    ADC_BufferCmd(ADC1, DISABLE);   //disable buffer
    ADC_ResetCalibration(ADC1);
    while(ADC_GetResetCalibrationStatus(ADC1));
    ADC_StartCalibration(ADC1);
    while(ADC_GetCalibrationStatus(ADC1));
    Calibrattion_Val = Get_CalibrationValue(ADC1);

}
void DMA_Tx_Init( DMA_Channel_TypeDef* DMA_CHx, u32 ppadr, u32 memadr, u16 bufsize)
{
    DMA_InitTypeDef DMA_InitStructure={0};

    RCC_AHBPeriphClockCmd( RCC_AHBPeriph_DMA1, ENABLE );

    DMA_DeInit(DMA_CHx);
    DMA_InitStructure.DMA_PeripheralBaseAddr = ppadr;
    DMA_InitStructure.DMA_MemoryBaseAddr = memadr;
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralSRC;
    DMA_InitStructure.DMA_BufferSize = bufsize;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
    DMA_InitStructure.DMA_Priority = DMA_Priority_VeryHigh;
    DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;
    DMA_Init( DMA_CHx, &DMA_InitStructure );
}

u16 Get_ConversionVal(s16 val)
{
    if((val+Calibrattion_Val)<0|| val==0) return 0;
    if((Calibrattion_Val+val)>4095||val==4095) return 4095;
    return (val+Calibrattion_Val);
}


#define CRY_THRESHOLD 200  // ����ֵ���Լ�������Ե���

//u16 TxBuf[1024];
//s16 Calibrattion_Val = 0;
uint8_t IsLikelyCry(s16 *buffer, uint16_t size)
{
    uint16_t zcr_val = Calc_ZeroCrossingRate(buffer, size);
    uint32_t energy_variation = Calc_EnergyVariation(buffer, size);

    // ������Щ�����ſ����ǿ���
    if (zcr_val > SPEECH_ZCR_THRESHOLD || energy_variation > SPEECH_ENERGY_VAR_THRESHOLD)
    {
        return 1;  // �����ǿ���
    }

    return 0;  // �ܿ�����˵�����򻷾���
}

void CryDetectTask()
{
    uint32_t total_amp = 0;
    uint16_t amp = 0;

    for (uint16_t i = 0; i < 1024; i++)
    {
        amp = abs(Get_ConversionVal(TxBuf[i]) - 2048); // ��2048Ϊ��̬����
        total_amp += amp;
    }

    uint16_t avg_amp = total_amp / 1024;

    printf("Avg Amp: %d\n", avg_amp);

    if (avg_amp > CRY_THRESHOLD)
    {
        printf(">>> Crying Detected! <<<\n");
        // ��Ҳ���Ե���LED��������������
    }
}



//����or˵����
int main(void)
{
    SystemCoreClockUpdate();
    Delay_Init();
    fft_cfg = kiss_fft_alloc(SAMPLE_SIZE, 0, NULL, NULL);  // 0 ��ʾ����FFT
    USART_Printf_Init(115200);
    printf("SystemClk:%d\r\n", SystemCoreClock);
    printf("ChipID:%08x\r\n", DBGMCU_GetCHIPID());

    ADC_Function_Init();
    printf("CalibrattionValue:%d\n", Calibrattion_Val);
    printf("CalibrattionValue:%d\n", Calibrattion_Val);





    DMA_Tx_Init(DMA1_Channel1, (u32)&ADC1->RDATAR, (u32)TxBuf, 1024);
    DMA_Cmd(DMA1_Channel1, ENABLE);

    ADC_RegularChannelConfig(ADC1, ADC_Channel_1, 1, ADC_SampleTime_239Cycles5);
    ADC_SoftwareStartConvCmd(ADC1, ENABLE);

            while (1)
        {
            uint32_t dc_offset_sum = 0;
            for (uint16_t i = 0; i < SAMPLE_SIZE; i++)
            {
                dc_offset_sum += Get_ConversionVal(TxBuf[i]);
            }
            uint16_t dc_offset = dc_offset_sum / SAMPLE_SIZE;

            uint32_t energy_sum = 0;
            uint16_t peak_val = 0;
            uint16_t zcr = 0;

            int16_t last_sample = Get_ConversionVal(TxBuf[0]) - dc_offset;

            for (uint16_t i = 0; i < SAMPLE_SIZE; i++)
            {
                int16_t sample = Get_ConversionVal(TxBuf[i]) - dc_offset;
                uint16_t abs_sample = abs(sample);

                if (abs_sample > peak_val)
                    peak_val = abs_sample;

                energy_sum += (int32_t)sample * sample;

                // ����ZCR
                if ((sample > 0 && last_sample < 0) || (sample < 0 && last_sample > 0))
                {
                    zcr++;
                }

                last_sample = sample;
            }


            uint32_t avg_energy = energy_sum / SAMPLE_SIZE;

            // ����ƽ������
            energy_buf[smooth_index] = avg_energy;
            peak_buf[smooth_index] = peak_val;
            zcr_buf[smooth_index] = zcr;

            // ����ƽ��ֵ
            uint32_t energy_sum_s = 0;
            uint16_t peak_sum_s = 0, zcr_sum_s = 0;
            for (uint8_t i = 0; i < SMOOTH_SIZE; i++)
            {
                energy_sum_s += energy_buf[i];
                peak_sum_s += peak_buf[i];
                zcr_sum_s += zcr_buf[i];
            }

            uint32_t smooth_energy = energy_sum_s / SMOOTH_SIZE;
            uint16_t smooth_peak = peak_sum_s / SMOOTH_SIZE;
            uint16_t smooth_zcr = zcr_sum_s / SMOOTH_SIZE;

            smooth_index = (smooth_index + 1) % SMOOTH_SIZE;

            // ��ӡ������Ϣ
            printf("Peak: %d, Energy: %ld, ZCR: %d\r\n", smooth_peak, smooth_energy, smooth_zcr);

            // �����жϿ�����˵��������
            if (smooth_energy > ENERGY_THRESHOLD && smooth_zcr > ZCR_THRESHOLD)
            {
                printf(">>> Crying Detected <<<\r\n");
            }
            else if (smooth_energy > ENERGY_THRESHOLD && smooth_zcr <= ZCR_THRESHOLD)
            {
                printf(">>> Speaking Detected <<<\r\n");
            }
            else
            {
                printf(">>> Quiet <<<\r\n");
            }
            if (smooth_energy > ENERGY_THRESHOLD && smooth_zcr > ZCR_THRESHOLD)
            {
                printf(">>> Crying Detected <<<\r\n");

                // 1. ��FFTǰ��׼������
                kiss_fft_cpx fft_in[SAMPLE_SIZE];
                kiss_fft_cpx fft_out[SAMPLE_SIZE];

                for (int i = 0; i < SAMPLE_SIZE; i++) {
                    int16_t sample = Get_ConversionVal(TxBuf[i]) - 2048;  // ȥƫ��
                    fft_in[i].r = sample;
                    fft_in[i].i = 0;
                }

                // 2. ִ��FFT
                kiss_fft(fft_cfg, fft_in, fft_out);

                // 3. ����Ƶ������������ֻ����ǰ�벿�֣�
                float low_energy = 0, mid_energy = 0, high_energy = 0;
                for (int i = 1; i < SAMPLE_SIZE / 2; i++) {
                    float mag = sqrtf(fft_out[i].r * fft_out[i].r + fft_out[i].i * fft_out[i].i);

                    // ���������Ϊ 8000Hz��ÿ��Ƶ��Ƶ��Ϊ 8000/1024 �� 7.8Hz
                    float freq = (8000.0f / SAMPLE_SIZE) * i;

                    if (freq < 500) {
                        low_energy += mag;
                    } else if (freq < 1500) {
                        mid_energy += mag;
                    } else {
                        high_energy += mag;
                    }
                }

                // 4. �����������
                if (low_energy > mid_energy && low_energy > high_energy) {
                    printf(">>> Sleepy Cry <<<\r\n");
                } else if (mid_energy > low_energy && mid_energy > high_energy) {
                    printf(">>> Angry Cry <<<\r\n");
                } else {
                    printf(">>> Hungry Cry <<<\r\n");
                }
                uint32_t low_val = (uint32_t)(low_energy * 1000);
                uint32_t mid_val = (uint32_t)(mid_energy * 1000);
                uint32_t high_val = (uint32_t)(high_energy * 1000);
                printf("Low: %lu Mid: %lu High: %lu\r\n", low_val, mid_val, high_val);







            }


            Delay_Ms(600);
        }
    }








//int main(void)
//{
//    u16 i;
//    SystemCoreClockUpdate();
//    Delay_Init();
//    USART_Printf_Init(115200);
//    printf("SystemClk:%d\r\n",SystemCoreClock);
//    printf( "ChipID:%08x\r\n", DBGMCU_GetCHIPID() );
//
//    ADC_Function_Init();
//    printf("CalibrattionValue:%d\n", Calibrattion_Val);
//
//    DMA_Tx_Init( DMA1_Channel1, (u32)&ADC1->RDATAR, (u32)TxBuf, 1024 );
//    DMA_Cmd( DMA1_Channel1, ENABLE );
//
//    ADC_RegularChannelConfig(ADC1, ADC_Channel_1, 1, ADC_SampleTime_239Cycles5 );
//    ADC_SoftwareStartConvCmd(ADC1, ENABLE);
//    Delay_Ms(50);
//    ADC_SoftwareStartConvCmd(ADC1, DISABLE);
//
//    for(i=0; i<1024; i++)
//    {
//        printf( "%04d\r\n", Get_ConversionVal(TxBuf[i]));
//        Delay_Ms(10);
//    }
//
//    while(1);
//}


