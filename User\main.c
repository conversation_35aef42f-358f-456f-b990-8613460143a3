
#include "debug.h"
#include "kiss_fft.h"
#include "kiss_fft_log.h"

/* Global Variable */
u16 TxBuf[1024];
s16 Calibrattion_Val = 0;




// 配置参数统一管理
#define SAMPLE_SIZE 1024  // 采样数据长度
#define FFT_SIZE 1024     // FFT变换长度
#define SMOOTH_SIZE 5     // 滑动平均窗口大小

// 基础检测阈值 - 根据实际数据调整
#define ENERGY_THRESHOLD 5000      // 能量阈值(提高以过滤噪声)
#define ZCR_THRESHOLD_HIGH 85      // 高ZCR阈值(说话声)
#define ZCR_THRESHOLD_LOW 45       // 低ZCR阈值(哭声下限)
#define PEAK_THRESHOLD 200         // 峰值阈值
#define CRY_ENERGY_MIN 50000       // 哭声最小能量阈值
#define CRY_ENERGY_MAX 2000000     // 哭声最大能量阈值

// 情绪分类频率范围(Hz)
#define FREQ_LOW_MIN 100           // 低频最小值
#define FREQ_LOW_MAX 400           // 低频最大值
#define FREQ_MID_MIN 400           // 中频最小值
#define FREQ_MID_MAX 1200          // 中频最大值
#define FREQ_HIGH_MIN 1200         // 高频最小值
#define FREQ_HIGH_MAX 3000         // 高频最大值

// 情绪识别阈值
#define ANGRY_ENERGY_RATIO 1.5f    // 愤怒哭声能量比例
#define HUNGRY_ZCR_RATIO 1.2f      // 饥饿哭声ZCR比例
#define SLEEPY_LOW_FREQ_RATIO 2.0f // 困倦哭声低频比例
#define SICK_HIGH_FREQ_RATIO 0.8f  // 生病哭声高频比例
#define BORED_MID_FREQ_RATIO 1.3f  // 无聊哭声中频比例

// 全局变量
uint32_t energy_buf[SMOOTH_SIZE] = {0};
uint16_t peak_buf[SMOOTH_SIZE] = {0};
uint16_t zcr_buf[SMOOTH_SIZE] = {0};
uint8_t smooth_index = 0;

kiss_fft_cfg fft_cfg;
kiss_fft_cpx fft_in[SAMPLE_SIZE];
kiss_fft_cpx fft_out[SAMPLE_SIZE];

// 情绪类型枚举
typedef enum {
    CRY_NONE = 0,     // 无哭声
    CRY_ANGRY,        // 愤怒
    CRY_HUNGRY,       // 饥饿
    CRY_SLEEPY,       // 困倦
    CRY_SICK,         // 生病
    CRY_BORED,        // 无聊
    CRY_PAIN,         // 疼痛
    CRY_UNKNOWN       // 未知类型
} CryType_t;

/*********************************************************************
 * @fn      ADC_Function_Init
 *
 * @brief   Initializes ADC collection.
 *
 * @return  none
 */
void ADC_Function_Init(void)
{
    ADC_InitTypeDef ADC_InitStructure={0};
    GPIO_InitTypeDef GPIO_InitStructure={0};

    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE );
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1, ENABLE );
    RCC_ADCCLKConfig(RCC_PCLK2_Div8);

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AIN;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    ADC_DeInit(ADC1);
    ADC_InitStructure.ADC_Mode = ADC_Mode_Independent;
    ADC_InitStructure.ADC_ScanConvMode = DISABLE;
    ADC_InitStructure.ADC_ContinuousConvMode = ENABLE;
    ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_None;
    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;
    ADC_InitStructure.ADC_NbrOfChannel = 1;
    ADC_Init(ADC1, &ADC_InitStructure);

    ADC_DMACmd(ADC1, ENABLE);
    ADC_Cmd(ADC1, ENABLE);

    ADC_BufferCmd(ADC1, DISABLE);   //disable buffer
    ADC_ResetCalibration(ADC1);
    while(ADC_GetResetCalibrationStatus(ADC1));
    ADC_StartCalibration(ADC1);
    while(ADC_GetCalibrationStatus(ADC1));
    Calibrattion_Val = Get_CalibrationValue(ADC1);

}
void DMA_Tx_Init( DMA_Channel_TypeDef* DMA_CHx, u32 ppadr, u32 memadr, u16 bufsize)
{
    DMA_InitTypeDef DMA_InitStructure={0};

    RCC_AHBPeriphClockCmd( RCC_AHBPeriph_DMA1, ENABLE );

    DMA_DeInit(DMA_CHx);
    DMA_InitStructure.DMA_PeripheralBaseAddr = ppadr;
    DMA_InitStructure.DMA_MemoryBaseAddr = memadr;
    DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralSRC;
    DMA_InitStructure.DMA_BufferSize = bufsize;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;
    DMA_InitStructure.DMA_Priority = DMA_Priority_VeryHigh;
    DMA_InitStructure.DMA_M2M = DMA_M2M_Disable;
    DMA_Init( DMA_CHx, &DMA_InitStructure );
}

u16 Get_ConversionVal(s16 val)
{
    if((val+Calibrattion_Val)<0|| val==0) return 0;
    if((Calibrattion_Val+val)>4095||val==4095) return 4095;
    return (val+Calibrattion_Val);
}

// 计算零交叉率
uint16_t Calc_ZeroCrossingRate(s16 *buffer, uint16_t size)
{
    uint16_t zcr = 0;
    for(uint16_t i = 1; i < size; i++) {
        if((buffer[i] > 0 && buffer[i-1] < 0) || (buffer[i] < 0 && buffer[i-1] > 0)) {
            zcr++;
        }
    }
    return zcr;
}

// 计算能量变化
uint32_t Calc_EnergyVariation(s16 *buffer, uint16_t size)
{
    uint32_t total_energy = 0, prev_energy = 0, energy_var = 0;
    uint16_t window_size = size / 4; // 分4个窗口

    for(uint8_t w = 0; w < 4; w++) {
        uint32_t window_energy = 0;
        for(uint16_t i = w * window_size; i < (w + 1) * window_size; i++) {
            window_energy += (uint32_t)(buffer[i] * buffer[i]);
        }
        if(w > 0) {
            energy_var += abs((int32_t)window_energy - (int32_t)prev_energy);
        }
        prev_energy = window_energy;
    }
    return energy_var / 3; // 平均变化量
}


// 高级哭声情绪识别函数
CryType_t AnalyzeCryEmotion(float low_energy, float mid_energy, float high_energy, uint16_t zcr, uint32_t avg_energy)
{
    float total_energy = low_energy + mid_energy + high_energy;
    if(total_energy < 100) return CRY_NONE; // 能量太低

    float low_ratio = low_energy / total_energy;
    float mid_ratio = mid_energy / total_energy;
    float high_ratio = high_energy / total_energy;

    // 愤怒哭声：高能量，中高频占主导
    if(avg_energy > ENERGY_THRESHOLD * ANGRY_ENERGY_RATIO && (mid_ratio + high_ratio) > 0.6f) {
        return CRY_ANGRY;
    }

    // 饥饿哭声：节奏性强，ZCR较高
    if(zcr > ZCR_THRESHOLD_LOW * HUNGRY_ZCR_RATIO && mid_ratio > 0.4f) {
        return CRY_HUNGRY;
    }

    // 困倦哭声：低频占主导，能量较低
    if(low_ratio > 0.5f && avg_energy < ENERGY_THRESHOLD * 0.8f) {
        return CRY_SLEEPY;
    }

    // 生病哭声：高频能量低，声音虚弱
    if(high_ratio < SICK_HIGH_FREQ_RATIO * 0.3f && avg_energy < ENERGY_THRESHOLD * 0.6f) {
        return CRY_SICK;
    }

    // 无聊哭声：中频占主导，间歇性
    if(mid_ratio > 0.4f && low_ratio < 0.3f) {
        return CRY_BORED;
    }

    // 疼痛哭声：突然高能量，高频尖锐
    if(avg_energy > ENERGY_THRESHOLD * 1.8f && high_ratio > 0.4f) {
        return CRY_PAIN;
    }

    return CRY_UNKNOWN;
}

// 获取哭声类型名称
const char* GetCryTypeName(CryType_t type)
{
    switch(type) {
        case CRY_ANGRY:  return "Angry";
        case CRY_HUNGRY: return "Hungry";
        case CRY_SLEEPY: return "Sleepy";
        case CRY_SICK:   return "Sick";
        case CRY_BORED:  return "Bored";
        case CRY_PAIN:   return "Pain";
        case CRY_NONE:   return "No Cry";
        default:         return "Unknown";
    }
}

// 简化的哭声检测任务（保留兼容性）
void CryDetectTask()
{
    uint32_t total_amp = 0;
    uint16_t amp = 0;

    for (uint16_t i = 0; i < SAMPLE_SIZE; i++) {
        amp = abs(Get_ConversionVal(TxBuf[i]) - 2048); // 以2048为静态基准
        total_amp += amp;
    }

    uint16_t avg_amp = total_amp / SAMPLE_SIZE;
    printf("Avg Amp: %d\n", avg_amp);

    if (avg_amp > 200) { // 简单幅度检测
        printf(">>> Crying Detected! <<<\n");
        // 这里可以添加LED指示或其他响应
    }
}



//����or˵����
int main(void)
{
    SystemCoreClockUpdate();
    Delay_Init();
    fft_cfg = kiss_fft_alloc(SAMPLE_SIZE, 0, NULL, NULL);  // 0 ��ʾ����FFT
    USART_Printf_Init(115200);
    printf("SystemClk:%d\r\n", SystemCoreClock);
    printf("ChipID:%08x\r\n", DBGMCU_GetCHIPID());

    ADC_Function_Init();
    printf("CalibrattionValue:%d\n", Calibrattion_Val);
    printf("CalibrattionValue:%d\n", Calibrattion_Val);





    DMA_Tx_Init(DMA1_Channel1, (u32)&ADC1->RDATAR, (u32)TxBuf, 1024);
    DMA_Cmd(DMA1_Channel1, ENABLE);

    ADC_RegularChannelConfig(ADC1, ADC_Channel_1, 1, ADC_SampleTime_239Cycles5);
    ADC_SoftwareStartConvCmd(ADC1, ENABLE);

            while (1)
        {
            uint32_t dc_offset_sum = 0;
            for (uint16_t i = 0; i < SAMPLE_SIZE; i++)
            {
                dc_offset_sum += Get_ConversionVal(TxBuf[i]);
            }
            uint16_t dc_offset = dc_offset_sum / SAMPLE_SIZE;

            uint32_t energy_sum = 0;
            uint16_t peak_val = 0;
            uint16_t zcr = 0;

            int16_t last_sample = Get_ConversionVal(TxBuf[0]) - dc_offset;

            for (uint16_t i = 0; i < SAMPLE_SIZE; i++)
            {
                int16_t sample = Get_ConversionVal(TxBuf[i]) - dc_offset;
                uint16_t abs_sample = abs(sample);

                if (abs_sample > peak_val)
                    peak_val = abs_sample;

                energy_sum += (int32_t)sample * sample;

                // ����ZCR
                if ((sample > 0 && last_sample < 0) || (sample < 0 && last_sample > 0))
                {
                    zcr++;
                }

                last_sample = sample;
            }


            uint32_t avg_energy = energy_sum / SAMPLE_SIZE;

            // ����ƽ������
            energy_buf[smooth_index] = avg_energy;
            peak_buf[smooth_index] = peak_val;
            zcr_buf[smooth_index] = zcr;

            // ����ƽ��ֵ
            uint32_t energy_sum_s = 0;
            uint16_t peak_sum_s = 0, zcr_sum_s = 0;
            for (uint8_t i = 0; i < SMOOTH_SIZE; i++)
            {
                energy_sum_s += energy_buf[i];
                peak_sum_s += peak_buf[i];
                zcr_sum_s += zcr_buf[i];
            }

            uint32_t smooth_energy = energy_sum_s / SMOOTH_SIZE;
            uint16_t smooth_peak = peak_sum_s / SMOOTH_SIZE;
            uint16_t smooth_zcr = zcr_sum_s / SMOOTH_SIZE;

            smooth_index = (smooth_index + 1) % SMOOTH_SIZE;

            // 打印详细监控信息
            printf("Peak: %d, Energy: %ld, ZCR: %d", smooth_peak, smooth_energy, smooth_zcr);

            // 添加分类判断提示
            if (smooth_energy >= CRY_ENERGY_MIN && smooth_energy <= CRY_ENERGY_MAX) {
                printf(" [E-OK]");
            }
            if (smooth_zcr >= ZCR_THRESHOLD_LOW && smooth_zcr <= ZCR_THRESHOLD_HIGH) {
                printf(" [Z-OK]");
            }
            printf("\r\n");

            // 改进的声音分类逻辑
            if (smooth_energy >= CRY_ENERGY_MIN && smooth_energy <= CRY_ENERGY_MAX &&
                smooth_zcr >= ZCR_THRESHOLD_LOW && smooth_zcr <= ZCR_THRESHOLD_HIGH) {
                printf(">>> Crying Detected <<<\r\n");
            }
            else if (smooth_energy > ENERGY_THRESHOLD && smooth_zcr > ZCR_THRESHOLD_HIGH) {
                printf(">>> Speaking Detected <<<\r\n");
            }
            else if (smooth_energy > ENERGY_THRESHOLD) {
                printf(">>> Other Sound <<<\r\n");
            }
            else {
                printf(">>> Quiet <<<\r\n");
            }

            // 详细哭声情绪分析 - 修正条件
            if (smooth_energy >= CRY_ENERGY_MIN && smooth_energy <= CRY_ENERGY_MAX &&
                smooth_zcr >= ZCR_THRESHOLD_LOW && smooth_zcr <= ZCR_THRESHOLD_HIGH) {
                printf(">>> Crying Detected <<<\r\n");

                // 1. 准备FFT输入数据
                for (int i = 0; i < SAMPLE_SIZE; i++) {
                    int16_t sample = Get_ConversionVal(TxBuf[i]) - 2048;  // 去偏置
                    fft_in[i].r = sample;
                    fft_in[i].i = 0;
                }

                // 2. 执行FFT变换
                kiss_fft(fft_cfg, fft_in, fft_out);

                // 3. 计算频段能量（采样率8000Hz，频率分辨率7.8Hz）
                float low_energy = 0, mid_energy = 0, high_energy = 0;
                for (int i = 1; i < SAMPLE_SIZE / 2; i++) {
                    float mag = sqrtf(fft_out[i].r * fft_out[i].r + fft_out[i].i * fft_out[i].i);
                    float freq = (8000.0f / SAMPLE_SIZE) * i;

                    if (freq >= FREQ_LOW_MIN && freq < FREQ_LOW_MAX) {
                        low_energy += mag;
                    } else if (freq >= FREQ_MID_MIN && freq < FREQ_MID_MAX) {
                        mid_energy += mag;
                    } else if (freq >= FREQ_HIGH_MIN && freq < FREQ_HIGH_MAX) {
                        high_energy += mag;
                    }
                }

                // 4. 情绪识别分析
                CryType_t cry_type = AnalyzeCryEmotion(low_energy, mid_energy, high_energy, smooth_zcr, smooth_energy);
                printf(">>> %s Cry Detected <<<\r\n", GetCryTypeName(cry_type));

                // 5. 输出频谱分析结果
                uint32_t low_val = (uint32_t)(low_energy * 100);
                uint32_t mid_val = (uint32_t)(mid_energy * 100);
                uint32_t high_val = (uint32_t)(high_energy * 100);
                printf("Freq Analysis - Low:%lu Mid:%lu High:%lu\r\n", low_val, mid_val, high_val);

                // 6. 输出情绪特征参数
                float total = low_energy + mid_energy + high_energy;
                if(total > 0) {
                    printf("Ratios - L:%.2f M:%.2f H:%.2f\r\n",
                           low_energy/total, mid_energy/total, high_energy/total);
                }
            }


            Delay_Ms(600);
        }
    }










